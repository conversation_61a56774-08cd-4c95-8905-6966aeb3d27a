import json
import os
from django.core.cache import cache
from django.db import connection
from django.test import RequestFactory
import typing as t
import responses as responses_

import pytest

from accounts.utils.api_services.otp import OTPApiService


@pytest.fixture
def load_json():
    """Pass path from the folder accounts/tests/fixtures"""

    def wrap(path):
        with open(
            os.path.join(os.getcwd(), f"accounts/tests/fixtures/{path}")
        ) as f:
            return json.load(f)

    return wrap


@pytest.fixture(autouse=True)
def media_storage(settings, tmpdir):
    settings.MEDIA_ROOT = tmpdir.strpath


@pytest.fixture
def request_factory() -> RequestFactory:
    return RequestFactory()


@pytest.fixture(scope="session", autouse=True)
def django_db_setup(django_db_blocker):
    with django_db_blocker.unblock():
        from django.apps import apps

        models_list = apps.get_models()
        for model in models_list:
            with connection.schema_editor() as schema_editor:
                schema_editor.create_model(model)

                if (
                    model._meta.db_table
                    not in connection.introspection.table_names()
                ):
                    raise ValueError(
                        "Table `{table_name}` is missing in test database.".format(
                            table_name=model._meta.db_table
                        )
                    )

        yield
        for model in models_list:
            with connection.schema_editor() as schema_editor:
                schema_editor.delete_model(model)


@pytest.fixture
def responses() -> t.Generator["responses_.RequestsMock", None, None]:
    with responses_.RequestsMock(assert_all_requests_are_fired=False) as res:
        # override_responses_real_send(res) #TODO Uncomment if moto is being used
        yield res
        # override_responses_real_send(None) #TODO Uncomment if moto is being used


@pytest.fixture(autouse=True)
def cache_clean():
    cache.clear()
    yield


@pytest.fixture
def mock_smsg_api(responses, load_json):
    def wrap(expected_response: t.Dict = None, status_code=200):
        if expected_response is None:
            expected_response = load_json("send_msg.json")
        return responses.add(
            responses.POST,
            OTPApiService().get_url(),
            json=expected_response,
            status=status_code,
        )

    yield wrap
