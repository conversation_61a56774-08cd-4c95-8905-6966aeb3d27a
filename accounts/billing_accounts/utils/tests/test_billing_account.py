from unittest import mock
from django.test import TestCase
from accounts.billing_accounts.utils.billing_account import (
    log_edited_fields,
    is_valid_gst_no,
    fetch_billing_account_list,
    count_billing_accounts_by_gst,
    count_billing_accounts_by_gst_in_duration,
    count_fraud_account_by_gst,
    count_fraud_account_by_gst_in_duration,
    count_billing_accounts_by_uan,
    count_billing_accounts_by_uan_in_duration,
    count_fraud_account_by_uan,
    count_fraud_account_by_uan_in_duration,
    update_gstin,
)
from accounts.billing_accounts.models import BillingAccountLogs
from accounts.billing_accounts.tests.factories import BillingAccountFactory
from accounts.core.tests.factories import StateCodeFactory
from django.core.exceptions import FieldError
from django.utils import timezone as tz
from accounts.billing_accounts import constants
from accounts.billing_accounts.exceptions import InvalidGstNumberException
from accounts.billing_accounts.utils.gst_parser import GSTDetail


class TestBillingAccount(TestCase):
    def setUp(self):
        self.today = tz.now()
        self.from_date = tz.now() - tz.timedelta(days=1)
        self.to_date = tz.now() + tz.timedelta(days=1)
        self.gst_no = "1234567890xyz10"
        self.uan = "abcxy1234567890"

    def test_valid_gst_no(self):
        assert is_valid_gst_no("12**********GHI") is False  # invalid pan
        assert is_valid_gst_no("A2**********GHI") is False  # invalid state code
        assert is_valid_gst_no("12ABCDEF-GHIJKL") is False

        assert is_valid_gst_no("27**********1Z5") is True
        assert is_valid_gst_no("08GREPM0253E1ZZ") is True
        assert is_valid_gst_no("07**********1Z9") is True
        assert is_valid_gst_no("07**********1Z") is False  # (14 chars only)

    def test_log_edited_fields_no_changes(self):
        ban_id = BillingAccountFactory.create().id
        old_data = {
            "gst_no": "123",
            "business_name": "ABC Pvt. Ltd",
            "state_id": "12",
        }
        new_data = {
            "gst_no": "123",
            "business_name": "ABC Pvt. Ltd",
            "state_id": "12",
        }

        log_edited_fields(ban_id, old_data, new_data)
        logs = BillingAccountLogs.objects.all()
        assert len(logs) == 0

    def test_log_edited_fields_some_changes(self):
        ban_id = BillingAccountFactory.create().id
        old_data = {
            "gst_no": "123",
            "business_name": "ABC Pvt. Ltd",
            "state_id": 12,
            "business_address": "Test address",
            "business_pan": "**********",
            "business_country": "IN",
            "business_state": "Goa",
        }
        new_data = {
            "gst_no": "456",
            "business_name": "XYZ Pvt. Ltd",
            "state_id": 12,
            "business_address": "Test address",
            "business_pan": "**********",
            "business_country": "IN",
            "business_state": "Goa",
        }

        log_edited_fields(ban_id, old_data, new_data)

        logs = BillingAccountLogs.objects.get(billing_account_id=ban_id)
        assert logs.billing_account_id == ban_id
        assert logs.gst_no == old_data["gst_no"]
        assert logs.business_name == old_data["business_name"]
        assert logs.state_id is None
        assert logs.business_address == ""
        assert logs.business_pan is None
        assert logs.business_country is None
        assert logs.business_state is None

    def test_log_edited_fields_all_fields_changed(self):
        ban_id = BillingAccountFactory.create().id
        state_id_old = StateCodeFactory.create().id
        state_id_new = StateCodeFactory.create().id
        old_data = {
            "gst_no": "123",
            "business_name": "ABC Pvt. Ltd",
            "state_id": state_id_old,
            "business_address": "Test address",
            "business_pan": "**********",
            "business_country": "IN",
            "business_state": "Goa",
        }
        new_data = {
            "gst_no": "456",
            "business_name": "XYZ Pvt. Ltd",
            "state_id": state_id_new,
            "business_address": "Test address new",
            "business_pan": "**********",
            "business_country": "US",
            "business_state": "Delhi",
        }

        log_edited_fields(ban_id, old_data, new_data)

        logs = BillingAccountLogs.objects.get(billing_account_id=ban_id)
        assert logs.billing_account_id == ban_id
        assert logs.gst_no == old_data["gst_no"]
        assert logs.business_name == old_data["business_name"]
        assert int(logs.state_id) == old_data["state_id"]
        assert logs.business_address == old_data["business_address"]
        assert logs.business_pan == old_data["business_pan"]
        assert logs.business_country == old_data["business_country"]
        assert logs.business_state == old_data["business_state"]

    def test_log_edited_with_old_fields_none(self):
        ban_id = BillingAccountFactory.create().id
        state_id_new = StateCodeFactory.create().id
        old_data = {
            "gst_no": None,
            "business_name": "ABC Pvt. Ltd",
            "state_id": 0,
            "business_address": "Test address",
            "business_pan": "NA",
            "business_country": "IN",
            "business_state": "Goa",
        }
        new_data = {
            "gst_no": "456",
            "business_name": "XYZ Pvt. Ltd",
            "state_id": state_id_new,
            "business_address": "Test address new",
            "business_pan": "**********",
            "business_country": "US",
            "business_state": "Delhi",
        }

        log_edited_fields(ban_id, old_data, new_data)

        logs = BillingAccountLogs.objects.get(billing_account_id=ban_id)
        assert logs.billing_account_id == ban_id
        assert logs.gst_no == old_data["gst_no"]
        assert logs.business_name == old_data["business_name"]
        assert int(logs.state_id) == old_data["state_id"]
        assert logs.business_address == old_data["business_address"]
        assert logs.business_pan == old_data["business_pan"]
        assert logs.business_country == old_data["business_country"]
        assert logs.business_state == old_data["business_state"]

    def test_fetch_billing_account_list_positive(self):
        BillingAccountFactory.create(
            ac_number="AC001", business_name="Test Business 1", status=1
        )
        BillingAccountFactory.create(
            ac_number="AC002", business_name="Test Business 2", status=0
        )

        # test case 1: Filtering by ac_number
        queryset = fetch_billing_account_list(
            "ac_number", "desc", ac_number="AC001"
        )
        self.assertEqual(queryset.count(), 1)
        self.assertEqual(queryset.first().ac_number, "AC001")

        # test case 2: Filtering by business_name
        queryset = fetch_billing_account_list(
            "business_name", "asc", business_name="Test Business 1"
        )
        self.assertEqual(queryset.count(), 1)
        self.assertEqual(queryset.first().business_name, "Test Business 1")

        # test case 3: Filtering by status
        queryset = fetch_billing_account_list("status", "desc", status=0)
        self.assertEqual(queryset.count(), 1)
        self.assertEqual(queryset.first().status, 0)

        # test case 4: Ordering by ac_number
        queryset = fetch_billing_account_list("ac_number", "asc")
        self.assertEqual(queryset.count(), 2)
        self.assertEqual(queryset.first().ac_number, "AC001")

    def test_fetch_billing_account_list_negative(self):
        # test case 1: Invalid sort parameter
        with self.assertRaises(FieldError):
            fetch_billing_account_list(
                "invalid_field", "desc", ac_number="AC001"
            )

        # test case 2: Missing kwargs
        queryset = fetch_billing_account_list("ac_number", "desc")
        self.assertEqual(queryset.count(), 0)

        # test case 3: Incorrect order parameter
        queryset = fetch_billing_account_list(
            "ac_number", "invalid_order", ac_number="AC001"
        )
        self.assertEqual(queryset.count(), 0)

    def test_count_billing_accounts_by_gst(self):

        BillingAccountFactory.create_batch(5, gst_no=self.gst_no)

        # Positive test
        count = count_billing_accounts_by_gst(self.gst_no)
        self.assertEqual(count, 5)

        # Negative test
        count = count_billing_accounts_by_gst("12345")
        self.assertEqual(count, 0)

    def test_count_billing_accounts_by_gst_in_duration(self):
        BillingAccountFactory.create_batch(
            3, gst_no=self.gst_no, created=self.today
        )

        # Positive test
        count = count_billing_accounts_by_gst_in_duration(
            self.gst_no, self.from_date, self.to_date
        )
        self.assertEqual(count, 3)

        # Negative test
        count = count_billing_accounts_by_gst_in_duration(
            "123456", self.from_date, self.to_date
        )
        self.assertEqual(count, 0)

    def test_count_fraud_account_by_gst(self):
        BillingAccountFactory.create_batch(
            2,
            gst_no=self.gst_no,
            verification_state=constants.BILLING_VERIFICATION_STATE_FRAUD,
        )

        # Positive test
        count = count_fraud_account_by_gst(self.gst_no)
        self.assertEqual(count, 2)

        # Negative test
        count = count_fraud_account_by_gst("1234568")
        self.assertEqual(count, 0)

    def test_count_fraud_account_by_gst_in_duration(self):
        BillingAccountFactory.create_batch(
            4,
            gst_no=self.gst_no,
            verification_state=constants.BILLING_VERIFICATION_STATE_FRAUD,
            created=self.today,
        )

        # Positive test
        count = count_fraud_account_by_gst_in_duration(
            self.gst_no, self.from_date, self.to_date
        )
        self.assertEqual(count, 4)

        # Negative test
        count = count_fraud_account_by_gst_in_duration(
            "*********", self.from_date, self.to_date
        )
        self.assertEqual(count, 0)

    def test_count_billing_accounts_by_uan(self):

        BillingAccountFactory.create_batch(5, uan=self.uan)

        # Positive test
        count = count_billing_accounts_by_uan(self.uan)
        self.assertEqual(count, 5)

        # Negative test
        count = count_billing_accounts_by_uan("12345")
        self.assertEqual(count, 0)

    def test_count_billing_accounts_by_uan_in_duration(self):
        BillingAccountFactory.create_batch(3, uan=self.uan, created=self.today)

        # Positive test
        count = count_billing_accounts_by_uan_in_duration(
            self.uan, self.from_date, self.to_date
        )
        self.assertEqual(count, 3)

        # Negative test
        count = count_billing_accounts_by_uan_in_duration(
            "123456", self.from_date, self.to_date
        )
        self.assertEqual(count, 0)

    def test_count_fraud_account_by_uan(self):
        BillingAccountFactory.create_batch(
            2,
            uan=self.uan,
            verification_state=constants.BILLING_VERIFICATION_STATE_FRAUD,
        )

        # Positive test
        count = count_fraud_account_by_uan(self.uan)
        self.assertEqual(count, 2)

        # Negative test
        count = count_fraud_account_by_uan("1234568")
        self.assertEqual(count, 0)

    def test_count_fraud_account_by_uan_in_duration(self):
        BillingAccountFactory.create_batch(
            4,
            uan=self.uan,
            verification_state=constants.BILLING_VERIFICATION_STATE_FRAUD,
            created=self.today,
        )

        # Positive test
        count = count_fraud_account_by_uan_in_duration(
            self.uan, self.from_date, self.to_date
        )
        self.assertEqual(count, 4)

        # Negative test
        count = count_fraud_account_by_uan_in_duration(
            "*********", self.from_date, self.to_date
        )
        self.assertEqual(count, 0)


class TestUpdateGstin(TestCase):
    @mock.patch(
        "accounts.billing_accounts.utils.billing_account.SurepassApi.gstin_details"
    )
    def test_update_gstin(self, mock_gstin_details):
        gst_no = "27**********1Z5"
        mock_gstin_details.return_value = GSTDetail(
            data={
                "gstin": gst_no,
                "legal_name": "Test Company",
                "gstin_status": "Active",
                "contact_details": {
                    "principal": {
                        "address": "123 Test Street, 400001",
                        "mobile": "**********",
                    }
                },
            },
            gstin=gst_no,
            legal_name="Test Company",
            pincode="400001",
            address="123 Test Street, 400001",
            status="Active",
        )
        stage = StateCodeFactory.create(code_for_gst="27")
        billing_account = BillingAccountFactory.create()
        update_gstin(billing_account, gst_no)
        billing_account.refresh_from_db()
        self.assertEqual(billing_account.gst_no, gst_no)
        self.assertEqual(billing_account.state_id, stage.id)
        self.assertEqual(billing_account.business_pan, "**********")
        self.assertEqual(billing_account.business_name, "Test Company")
        self.assertEqual(
            billing_account.business_address, "123 Test Street, 400001"
        )
        self.assertEqual(billing_account.business_city, stage.name)
        self.assertEqual(billing_account.business_state, stage.name)
        self.assertEqual(billing_account.business_pincode, "400001")
        self.assertEqual(billing_account.business_country, "India")

    def test_update_gstin_invalid_gst_no(self):
        billing_account = BillingAccountFactory.create()
        with self.assertRaises(InvalidGstNumberException) as context:
            update_gstin(billing_account, "123456xyz10")
        self.assertEqual(str(context.exception), "Invalid GSTIN number.")

    @mock.patch(
        "accounts.billing_accounts.utils.billing_account.SurepassApi.gstin_details"
    )
    def test_update_gstin_inactive_gst_no(self, mock_gstin_details):
        gst_no = "27**********1Z5"
        mock_gstin_details.return_value = GSTDetail(
            data={
                "gstin": gst_no,
                "legal_name": "Test Company",
                "gstin_status": "Inactive",
                "contact_details": {
                    "principal": {
                        "address": "123 Test Street, 400001",
                        "mobile": "**********",
                    }
                },
            },
            gstin=gst_no,
            legal_name="Test Company",
            pincode="400001",
            address="123 Test Street, 400001",
            status="Inactive",
        )
        billing_account = BillingAccountFactory.create()
        with self.assertRaises(InvalidGstNumberException) as context:
            update_gstin(billing_account, "27**********1Z5")
        self.assertEqual(str(context.exception), "GSTIN is not active.")
