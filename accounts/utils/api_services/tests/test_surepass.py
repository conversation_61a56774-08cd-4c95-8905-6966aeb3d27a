import json
import responses
from django.test import TestCase
from rest_framework import status
from unittest.mock import patch, MagicMock

from accounts.billing_accounts.exceptions import InvalidGstNumberException
from accounts.utils.api_services.surepass import SurepassApi
from accounts.kyc.cache_handler import GstCacheHandler
from accounts.billing_accounts.utils.gst_parser import SurepassGSTParser


class TestSurepassApi(TestCase):
    def setUp(self):
        self.surepass_api = SurepassApi()
        self.gst_number = "27**********1ZV"
        self.mock_response_success = {
            "data": {
                "contact_details": {
                    "principal": {
                        "address": "123 Test Street, 400001",
                        "email": "<EMAIL>",
                        "mobile": "**********",
                        "nature_of_business": "Recipient of Goods or Services",
                    },
                    "additional": [],
                },
                "gstin": "27**********1ZV",
                "pan_number": "**********",
                "business_name": "Test Company",
                "legal_name": "Test Company",
                "gstin_status": "Active",
            },
            "status_code": 200,
            "success": True,
        }
        self.mock_response_error = {
            "success": False,
            "status_code": 400,
            "message": "Invalid GSTIN number",
        }
        self.api_url = (
            f"{self.surepass_api.HOST}/api/v1/corporate/gstin-advanced"
        )

    @responses.activate
    def test_gstin_details_success(self):
        # Setup mock response
        responses.add(
            responses.POST,
            self.api_url,
            json=self.mock_response_success,
            status=status.HTTP_200_OK,
        )

        # Call the method
        gst_detail = self.surepass_api.gstin_details(self.gst_number)

        # Verify the response
        self.assertEqual(gst_detail.gstin, "27**********1ZV")
        self.assertEqual(gst_detail.legal_name, "Test Company")
        self.assertEqual(gst_detail.address, "123 Test Street, 400001")
        self.assertEqual(gst_detail.state_code, "27")
        self.assertEqual(gst_detail.pincode, "400001")
        self.assertEqual(gst_detail.pan, "**********")

        # Verify API call
        self.assertEqual(len(responses.calls), 1)
        self.assertEqual(
            json.loads(responses.calls[0].request.body),
            {"id_number": self.gst_number},
        )

    @responses.activate
    def test_gstin_details_error(self):
        # Setup mock response
        responses.add(
            responses.POST,
            self.api_url,
            json=self.mock_response_error,
            status=status.HTTP_400_BAD_REQUEST,
        )

        # Verify exception is raised
        with self.assertRaises(InvalidGstNumberException) as context:
            self.surepass_api.gstin_details(self.gst_number)

        self.assertEqual(str(context.exception), "Invalid GSTIN number")

        # Verify API call
        self.assertEqual(len(responses.calls), 1)
        self.assertEqual(
            json.loads(responses.calls[0].request.body),
            {"id_number": self.gst_number},
        )

    @responses.activate
    def test_gstin_details_500_error(self):
        # Setup mock response for 500 error
        responses.add(
            responses.POST,
            self.api_url,
            json={
                "success": False,
                "status_code": 500,
                "message": "Internal Server Error",
            },
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )

        # Verify exception is raised
        with self.assertRaises(InvalidGstNumberException) as context:
            self.surepass_api.gstin_details(self.gst_number)

        self.assertEqual(str(context.exception), "Internal Server Error")

        # Verify API call
        self.assertEqual(len(responses.calls), 1)
        self.assertEqual(
            json.loads(responses.calls[0].request.body),
            {"id_number": self.gst_number},
        )

    @responses.activate
    def test_gstin_details_from_api_success(self):
        """Test gstin_details_from_api method directly."""
        # Setup mock response
        responses.add(
            responses.POST,
            self.api_url,
            json=self.mock_response_success,
            status=status.HTTP_200_OK,
        )

        # Call the method
        gst_detail = self.surepass_api.gstin_details_from_api(self.gst_number)

        # Verify the response
        self.assertEqual(gst_detail.gstin, "27**********1ZV")
        self.assertEqual(gst_detail.legal_name, "Test Company")
        self.assertEqual(gst_detail.address, "123 Test Street, 400001")

        # Verify API call
        self.assertEqual(len(responses.calls), 1)

    @patch("accounts.kyc.cache_handler.GstCacheHandler.get")
    @patch("accounts.kyc.cache_handler.GstCacheHandler.set")
    @responses.activate
    def test_gstin_details_with_cache_miss(
        self, mock_cache_set, mock_cache_get
    ):
        """Test gstin_details method when cache is empty."""
        # Setup cache miss
        mock_cache_get.return_value = None

        # Setup mock API response
        responses.add(
            responses.POST,
            self.api_url,
            json=self.mock_response_success,
            status=status.HTTP_200_OK,
        )

        # Call the method
        gst_detail = self.surepass_api.gstin_details(self.gst_number)

        # Verify the response
        self.assertEqual(gst_detail.gstin, "27**********1ZV")
        self.assertEqual(gst_detail.legal_name, "Test Company")

        # Verify cache was checked and set
        mock_cache_get.assert_called_once()
        mock_cache_set.assert_called_once_with(gst_detail)

        # Verify API was called
        self.assertEqual(len(responses.calls), 1)

    @patch("accounts.kyc.cache_handler.GstCacheHandler.get")
    @patch("accounts.billing_accounts.utils.gst_parser.SurepassGSTParser.parse")
    def test_gstin_details_with_cache_hit(
        self, mock_parser_parse, mock_cache_get
    ):
        """Test gstin_details method when cache has data."""
        # Setup cache hit
        cached_data = self.mock_response_success["data"]
        mock_cache_get.return_value = cached_data

        # Setup mock parser
        mock_gst_detail = MagicMock()
        mock_gst_detail.gstin = "27**********1ZV"
        mock_gst_detail.legal_name = "Test Company"
        mock_parser_parse.return_value = mock_gst_detail

        # Call the method
        gst_detail = self.surepass_api.gstin_details(self.gst_number)

        # Verify the response
        self.assertEqual(gst_detail.gstin, "27**********1ZV")
        self.assertEqual(gst_detail.legal_name, "Test Company")

        # Verify cache was checked and parser was called
        mock_cache_get.assert_called_once()
        mock_parser_parse.assert_called_once_with()

    @patch("accounts.kyc.cache_handler.GstCacheHandler.get")
    @patch("accounts.billing_accounts.utils.gst_parser.SurepassGSTParser.parse")
    @patch("accounts.kyc.cache_handler.GstCacheHandler.set")
    @responses.activate
    def test_gstin_details_cache_parse_error_fallback_to_api(
        self, mock_cache_set, mock_parser_parse, mock_cache_get
    ):
        """Test gstin_details method when cached data parsing fails."""
        # Setup cache hit but parsing fails
        cached_data = self.mock_response_success["data"]
        mock_cache_get.return_value = cached_data
        mock_parser_parse.side_effect = Exception("Parse error")

        # Setup mock API response
        responses.add(
            responses.POST,
            self.api_url,
            json=self.mock_response_success,
            status=status.HTTP_200_OK,
        )

        # Call the method
        gst_detail = self.surepass_api.gstin_details(self.gst_number)

        # Verify the response
        self.assertEqual(gst_detail.gstin, "27**********1ZV")
        self.assertEqual(gst_detail.legal_name, "Test Company")

        # Verify cache was checked, parsing failed, and API was called
        mock_cache_get.assert_called_once()
        mock_parser_parse.assert_called_once()
        mock_cache_set.assert_called_once_with(gst_detail)

        # Verify API was called as fallback
        self.assertEqual(len(responses.calls), 1)
