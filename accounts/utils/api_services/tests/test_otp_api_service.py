import json
import pytest
import responses
from unittest.mock import patch, MagicMock
from rest_framework import status

from accounts.utils.api_services.otp import OTPApiService, BaseOtpApiService
from accounts.utils.api_services.enums import OTPVia
from accounts.utils.api_services.exceptions import OTPApiException
from accounts.exceptions import ExternalAPIException


@pytest.fixture
def otp_test_data():
    return {
        "service": OTPApiService(),
        "phone_number": "**********",
        "country_code": "91",
        "otp": "1234",
    }


@pytest.mark.unittest
@pytest.mark.parametrize(
    "base_url,expected_url",
    [
        ("http://test-api.com/", "http://test-api.com/index-v3.php"),
        ("http://test-api.com", "http://test-api.com/index-v3.php"),
    ],
)
def test_otp_api_service_get_url(base_url, expected_url):
    service = OTPApiService()
    service.BASE_URL = base_url
    url = service.get_url()
    assert url == expected_url


@pytest.mark.unittest
@pytest.mark.parametrize(
    "country_code,phone_number,via,expected_app, expected_template_slug",
    [
        (
            "91",
            "**********",
            OTPVia.sms,
            "myoperator.otp",
            "myoperator-otp-sms",
        ),
        (
            "91",
            "**********",
            OTPVia.call,
            "myoperator.otp_call",
            "myoperator-otp-call",
        ),
        (
            "1",
            "1234567890",
            OTPVia.sms,
            "myoperator.otp.international",
            "myoperator-otp-sms",
        ),
        (
            "1",
            "1234567890",
            OTPVia.call,
            "myoperator.otp_call.international",
            "myoperator-otp-call",
        ),
    ],
)
def test_otp_api_service_send_otp_sms_india_success(
    load_json,
    otp_test_data,
    mock_smsg_api,
    country_code,
    phone_number,
    via,
    expected_app,
    expected_template_slug,
):
    res = mock_smsg_api()
    service = OTPApiService()
    status_code, response = service.send_otp(
        phone_number=phone_number,
        country_code=country_code,
        otp=otp_test_data["otp"],
        via=via,
    )
    assert status_code == 200
    assert response == load_json("send_msg.json")
    request_body = json.loads(res.calls[0].request.body)
    expected_payload = {
        "template_slug": expected_template_slug,
        "app": expected_app,
        "county_code": country_code,
        "send_to": phone_number,
        "params": {"otp": otp_test_data["otp"]},
    }
    assert request_body == expected_payload


@pytest.mark.parametrize(
    "status_code,error_response,expected_error_msg",
    [
        (
            429,
            {"success": False, "message": "Rate limit exceeded"},
            "sms OTP attempt limit exceeded",
        ),
        (
            500,
            {"success": False, "message": "Internal server error"},
            "Failed to send sms OTP",
        ),
    ],
)
def test_otp_api_service_send_otp_error_responses(
    mock_smsg_api,
    status_code,
    error_response,
    expected_error_msg,
    otp_test_data,
):
    res = mock_smsg_api(error_response, status_code)

    service = OTPApiService()

    with pytest.raises(OTPApiException) as exc_info:
        service.send_otp(
            phone_number=otp_test_data["phone_number"],
            country_code=otp_test_data["country_code"],
            otp=otp_test_data["otp"],
            via=OTPVia.sms,
        )
    assert expected_error_msg in str(exc_info.value)
    assert f"status: {status_code}" in str(exc_info.value)
    assert res.call_count == 1


@patch("accounts.utils.api_services.otp.OTPApiService.post_json")
def test_otp_api_service_send_otp_external_api_exception(
    mock_post_json, otp_test_data
):
    mock_post_json.side_effect = ExternalAPIException("Network error")
    service = OTPApiService()
    with pytest.raises(OTPApiException, match="API call failed"):
        service.send_otp(
            phone_number=otp_test_data["phone_number"],
            country_code=otp_test_data["country_code"],
            otp=otp_test_data["otp"],
            via=OTPVia.sms,
        )


@patch("accounts.utils.api_services.otp.OTPApiService.post_json")
def test_otp_api_service_send_otp_json_decode_error(
    mock_post_json, otp_test_data
):
    mock_response = MagicMock()
    mock_response.json.side_effect = json.JSONDecodeError("Invalid JSON", "", 0)
    mock_post_json.return_value = mock_response
    service = OTPApiService()
    with pytest.raises(OTPApiException, match="Failed to decode JSON response"):
        service.send_otp(
            phone_number=otp_test_data["phone_number"],
            country_code=otp_test_data["country_code"],
            otp=otp_test_data["otp"],
            via=OTPVia.sms,
        )
