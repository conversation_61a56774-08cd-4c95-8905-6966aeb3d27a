import logging
import typing as t
import json
from rest_framework import status

from accounts.kyc.cache_handler import GstCacheHandler
from .base import SurepassBase
from accounts.billing_accounts.utils.gst_parser import SurepassGSTParser
from accounts.billing_accounts.exceptions import InvalidGstNumberException

if t.TYPE_CHECKING:
    from accounts.billing_accounts.utils.gst_parser import GSTDetail

logger = logging.getLogger(__name__)


class SurepassApi(SurepassBase):
    def gstin_details_from_api(self, gst_number: str) -> "GSTDetail":
        url = f"{self.HOST}/api/v1/corporate/gstin-advanced"
        data = json.dumps({"id_number": gst_number})
        response = self.post_json(url, data=data, timeout=self.TIMEOUT)
        response_json = json.loads(response.text)
        if response.status_code == status.HTTP_200_OK:
            return SurepassGSTParser(response_json["data"]).parse()

        message = response_json.get("message", "Invalid GSTIN number.")
        raise InvalidGstNumberException(message)

    def gstin_details(self, gst_number: str):
        gst_cache_handler = GstCacheHandler(gst_number)
        cached_data = gst_cache_handler.get()
        if cached_data:
            logger.info("Using cached GST data")
            logger.debug(cached_data)
            try:
                return SurepassGSTParser(cached_data).parse()
            except Exception as e:
                logger.error(
                    f"Error parsing cached GST data: {e}", exc_info=True
                )

        logger.info("Fetching GST data from API")
        gst_data: GSTDetail = SurepassApi().gstin_details_from_api(gst_number)
        logger.debug(gst_data.data)
        gst_cache_handler.set(gst_data)
        return gst_data
