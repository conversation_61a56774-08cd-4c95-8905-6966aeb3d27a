from abc import ABC

from django.core.cache import cache


class BaseCacheHandler(ABC):
    def __init__(self, key: str) -> None:
        self._key = key

    def key(self):
        return self._key

    def get(self):
        return cache.get(self._key)

    # @abstractmethod
    def save(self, data, timeout):
        return cache.set(self._key, data, timeout)

    def delete(self):
        cache.delete(self._key)

    def expire(self, time=0):
        cache.expire(self._key, time)

    def ttl(self):
        return cache.ttl(self._key)

    def exists(self):
        return True if cache.get(self._key, None) else False

    def incr(self, timeout=0) -> int:
        try:
            return cache.incr(self._key)
            # cache.touch(self._key, timeout=timeout)
        except ValueError:
            cache.set(self._key, 1, timeout=timeout)
            return 1


class BaseCacheHandler_V2(ABC):
    def get(self, key: str):
        return cache.get(key)

    def save(self, key: str, data, timeout: int):
        return cache.set(key, data, timeout)

    def delete(self, key: str):
        cache.delete(key)

    def exists(self, key: str):
        return True if cache.get(key, None) else False

    def incr(self, key: str, timeout: int = None) -> int:
        try:
            return cache.incr(key)
        except ValueError:
            self.save(key, 1, timeout=timeout)
            return 1


class BaseOtpCacheHandler(BaseCacheHandler_V2):
    MAX_ATTEMPTS = 10

    def __init__(self):
        self.attempt_key = None

    def attempt_allowed(self, max_attempts: int = MAX_ATTEMPTS) -> bool:
        no_of_attempts = self.get(self.attempt_key)
        if no_of_attempts is not None and no_of_attempts >= max_attempts:
            return False
        return True
