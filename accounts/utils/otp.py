import random
from typing import Tuple

import phonenumbers

from accounts.utils.api_services.exceptions import OTPApiException
from accounts.utils.cache_handler import BaseOtpCacheHandler
from accounts.exceptions import OTPException
from accounts.utils.api_services.otp import BaseOtpApiService


class BaseOtpHandler:
    def __init__(self):
        self._send_attempt_handler: BaseOtpCacheHandler = None
        self._verification_attempt_handler: BaseOtpCacheHandler = None
        self._otp_cache_handler: BaseOtpCacheHandler = None
        self._api_service: BaseOtpApiService = None

    # ------------ static methods ----------
    @staticmethod
    def generate_otp():
        # Generate a random 4-digit number
        random_number = random.randint(1000, 9999)
        return random_number

    # ------------ properties(getters & setters) ----------

    @property
    def otp_cache_handler(self):
        if not self._otp_cache_handler:
            raise ValueError("OTP cache handler is not initialized.")
        return self._otp_cache_handler

    @property
    def api_service(self):
        if not self._api_service:
            raise ValueError("API service is not initialized.")
        return self._api_service

    @otp_cache_handler.setter
    def otp_cache_handler(self, handler: BaseOtpCacheHandler):
        self._otp_cache_handler = handler

    @api_service.setter
    def api_service(self, service: BaseOtpApiService):
        self._api_service = service

    # ------------ instance methods ----------

    def get_country_code_and_phone_number(self, number: str) -> Tuple[str, str]:
        try:
            phone_number = phonenumbers.parse(number, None)
            if phone_number:
                return phone_number.country_code, phone_number.national_number
        except phonenumbers.NumberParseException:
            pass

        raise OTPException("Invalid phone number")

    def is_send_max_attempts_reached(self) -> bool:
        return not self.otp_cache_handler.attempt_allowed()

    def send(self, *args, **kwargs):
        """Send OTP via the configured API service."""
        try:
            self._api_service.send_otp(*args, **kwargs)
        except OTPApiException as e:
            raise OTPException("Failed to send OTP") from e
