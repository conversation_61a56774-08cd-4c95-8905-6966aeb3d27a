import pytest


from accounts.utils.cache_handler import BaseOtpCacheHandler


@pytest.mark.unittest
def test_base_otp_cache_handler_attempt_allowed_under_limit():
    """Test attempt_allowed returns True when under limit."""
    handler = BaseOtpCacheHandler()
    handler.attempt_key = "test_key"
    for _ in range(5):
        handler.incr(
            handler.attempt_key,
        )
    result = handler.attempt_allowed(max_attempts=2)
    assert result is False
    result = handler.attempt_allowed(max_attempts=20)
    assert result is True
