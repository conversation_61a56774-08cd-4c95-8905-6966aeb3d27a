import pytest
from unittest.mock import patch

from accounts.utils.cache_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BaseOtpCacheHandler


class TestBaseCacheHandler:
    """Test cases for BaseCacheHandler."""

    def test_init(self):
        """Test initialization of BaseCacheHandler."""
        key = "test_key"
        handler = <PERSON>Cache<PERSON><PERSON><PERSON>(key)

        assert handler._key == key

    def test_key_property(self):
        """Test key property returns the correct key."""
        key = "test_cache_key"
        handler = BaseCacheHandler(key)

        assert handler.key() == key

    @patch("accounts.utils.cache_handler.cache")
    def test_get(self, mock_cache):
        """Test get method."""
        mock_cache.get.return_value = "cached_value"

        handler = <PERSON>CacheHandler("test_key")
        result = handler.get()

        assert result == "cached_value"
        mock_cache.get.assert_called_once_with("test_key")

    @patch("accounts.utils.cache_handler.cache")
    def test_save(self, mock_cache):
        """Test save method."""
        mock_cache.set.return_value = True

        handler = Base<PERSON>ache<PERSON>andler("test_key")
        data = {"test": "data"}
        timeout = 3600

        result = handler.save(data, timeout)

        assert result is True
        mock_cache.set.assert_called_once_with("test_key", data, timeout)

    @patch("accounts.utils.cache_handler.cache")
    def test_delete(self, mock_cache):
        """Test delete method."""
        handler = BaseCacheHandler("test_key")
        handler.delete()

        mock_cache.delete.assert_called_once_with("test_key")

    @patch("accounts.utils.cache_handler.cache")
    def test_expire(self, mock_cache):
        """Test expire method with default time."""
        handler = BaseCacheHandler("test_key")
        handler.expire()

        mock_cache.expire.assert_called_once_with("test_key", 0)

    @patch("accounts.utils.cache_handler.cache")
    def test_expire_with_custom_time(self, mock_cache):
        """Test expire method with custom time."""
        handler = BaseCacheHandler("test_key")
        custom_time = 1800
        handler.expire(custom_time)

        mock_cache.expire.assert_called_once_with("test_key", custom_time)

    @patch("accounts.utils.cache_handler.cache")
    def test_ttl(self, mock_cache):
        """Test ttl method."""
        mock_cache.ttl.return_value = 3600

        handler = BaseCacheHandler("test_key")
        result = handler.ttl()

        assert result == 3600
        mock_cache.ttl.assert_called_once_with("test_key")

    @patch("accounts.utils.cache_handler.cache")
    def test_exists_true(self, mock_cache):
        """Test exists method returns True when key exists."""
        mock_cache.get.return_value = "some_value"

        handler = BaseCacheHandler("test_key")
        result = handler.exists()

        assert result is True
        mock_cache.get.assert_called_once_with("test_key", None)

    @patch("accounts.utils.cache_handler.cache")
    def test_exists_false(self, mock_cache):
        """Test exists method returns False when key doesn't exist."""
        mock_cache.get.return_value = None

        handler = BaseCacheHandler("test_key")
        result = handler.exists()

        assert result is False
        mock_cache.get.assert_called_once_with("test_key", None)

    @patch("accounts.utils.cache_handler.cache")
    def test_incr_existing_key(self, mock_cache):
        """Test incr method with existing key."""
        mock_cache.incr.return_value = 5

        handler = BaseCacheHandler("test_key")
        result = handler.incr(timeout=3600)

        assert result == 5
        mock_cache.incr.assert_called_once_with("test_key")
        mock_cache.set.assert_not_called()

    @patch("accounts.utils.cache_handler.cache")
    def test_incr_new_key(self, mock_cache):
        """Test incr method with new key (ValueError raised)."""
        mock_cache.incr.side_effect = ValueError("Key does not exist")
        mock_cache.set.return_value = True

        handler = BaseCacheHandler("test_key")
        result = handler.incr(timeout=3600)

        assert result == 1
        mock_cache.incr.assert_called_once_with("test_key")
        mock_cache.set.assert_called_once_with("test_key", 1, timeout=3600)

    @patch("accounts.utils.cache_handler.cache")
    def test_incr_default_timeout(self, mock_cache):
        """Test incr method with default timeout."""
        mock_cache.incr.side_effect = ValueError("Key does not exist")
        mock_cache.set.return_value = True

        handler = BaseCacheHandler("test_key")
        result = handler.incr()

        assert result == 1
        mock_cache.set.assert_called_once_with("test_key", 1, timeout=0)


class TestBaseOtpCacheHandler:
    """Test cases for BaseOtpCacheHandler."""

    def test_init(self):
        """Test initialization of BaseOtpCacheHandler."""
        handler = BaseOtpCacheHandler()

        assert handler.attempt_key is None
        assert handler.MAX_ATTEMPTS == 10

    def test_inheritance(self):
        """Test that BaseOtpCacheHandler inherits from BaseCacheHandler_V2."""
        from accounts.utils.cache_handler import BaseCacheHandler_V2

        handler = BaseOtpCacheHandler()
        assert isinstance(handler, BaseCacheHandler_V2)

    @patch("accounts.utils.cache_handler.cache")
    def test_incr_calls_super_with_ttl(self, mock_cache):
        """Test incr method calls super().incr with TTL."""
        mock_cache.incr.return_value = 3

        handler = BaseOtpCacheHandler()
        handler.attempt_key = "test_key"

        result = handler.incr("test_key", timeout=1800)

        assert result == 3
        mock_cache.incr.assert_called_once_with("test_key")

    @patch("accounts.utils.cache_handler.cache")
    def test_incr_new_key_with_ttl(self, mock_cache):
        """Test incr method with new key uses TTL."""
        mock_cache.incr.side_effect = ValueError("Key does not exist")
        mock_cache.set.return_value = True

        handler = BaseOtpCacheHandler()
        handler.attempt_key = "test_key"

        result = handler.incr("test_key", timeout=1800)

        assert result == 1
        mock_cache.set.assert_called_once_with("test_key", 1, timeout=1800)

    @patch("accounts.utils.cache_handler.cache")
    def test_attempt_allowed_under_limit(self, mock_cache):
        """Test attempt_allowed returns True when under limit."""
        mock_cache.get.return_value = 5

        handler = BaseOtpCacheHandler()
        handler.attempt_key = "test_key"

        result = handler.attempt_allowed(max_attempts=10)

        assert result is True
        mock_cache.get.assert_called_once_with("test_key")

    @patch("accounts.utils.cache_handler.cache")
    def test_attempt_allowed_at_limit(self, mock_cache):
        """Test attempt_allowed returns False when at limit."""
        mock_cache.get.return_value = 10

        handler = BaseOtpCacheHandler()
        handler.attempt_key = "test_key"

        result = handler.attempt_allowed(max_attempts=10)

        assert result is False
        mock_cache.get.assert_called_once_with("test_key")

    @patch("accounts.utils.cache_handler.cache")
    def test_attempt_allowed_over_limit(self, mock_cache):
        """Test attempt_allowed returns False when over limit."""
        mock_cache.get.return_value = 15

        handler = BaseOtpCacheHandler()
        handler.attempt_key = "test_key"

        result = handler.attempt_allowed(max_attempts=10)

        assert result is False
        mock_cache.get.assert_called_once_with("test_key")

    @patch("accounts.utils.cache_handler.cache")
    def test_attempt_allowed_no_attempts(self, mock_cache):
        """Test attempt_allowed returns True when no attempts made."""
        mock_cache.get.return_value = None

        handler = BaseOtpCacheHandler()
        handler.attempt_key = "test_key"

        result = handler.attempt_allowed()

        assert result is True
        mock_cache.get.assert_called_once_with("test_key")

    @patch("accounts.utils.cache_handler.cache")
    def test_attempt_allowed_zero_attempts(self, mock_cache):
        """Test attempt_allowed returns True when zero attempts."""
        mock_cache.get.return_value = 0

        handler = BaseOtpCacheHandler()
        handler.attempt_key = "test_key"

        result = handler.attempt_allowed()

        assert result is True
        mock_cache.get.assert_called_once_with("test_key")

    @patch("accounts.utils.cache_handler.cache")
    def test_save_calls_cache_set(self, mock_cache):
        """Test save method calls cache.set."""
        mock_cache.set.return_value = True

        handler = BaseOtpCacheHandler()

        value = {"test": "data"}
        result = handler.save("test_key", value, 1800)

        assert result is True
        mock_cache.set.assert_called_once_with("test_key", value, 1800)

    def test_class_attributes(self):
        """Test class attributes have correct default values."""
        assert BaseOtpCacheHandler.MAX_ATTEMPTS == 10

    def test_subclass_can_override_attributes(self):
        """Test that subclasses can override MAX_ATTEMPTS."""

        class CustomOtpHandler(BaseOtpCacheHandler):
            MAX_ATTEMPTS = 5

        handler = CustomOtpHandler()
        assert handler.MAX_ATTEMPTS == 5
