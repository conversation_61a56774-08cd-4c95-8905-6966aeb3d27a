import pytest
from unittest.mock import patch, MagicMock
import phonenumbers

from accounts.utils.otp import BaseOtpHandler
from accounts.utils.cache_handler import BaseOtpCacheHandler
from accounts.utils.api_services.otp import BaseOtpApiService
from accounts.utils.api_services.exceptions import OTPApiException
from accounts.exceptions import OTPException


@pytest.mark.unittest
def test_base_otp_handler_generate_otp():
    otp = BaseOtpHandler.generate_otp()
    assert isinstance(otp, int)
    assert 1000 <= otp <= 9999


@pytest.mark.unittest
def test_base_otp_handler_otp_cache_handler_property_not_initialized():
    handler = BaseOtpHandler()
    with pytest.raises(
        ValueError, match="OTP cache handler is not initialized"
    ):
        _ = handler.otp_cache_handler


@pytest.mark.unittest
def test_base_otp_handler_api_service_property_not_initialized():
    handler = BaseOtpHandler()
    with pytest.raises(ValueError, match="API service is not initialized"):
        _ = handler.api_service


@pytest.mark.unittest
@pytest.mark.parametrize(
    "phone_number,expected_country_code,expected_phone_number",
    [
        ("+************", 91, **********),  # Indian number
        ("+***********", 1, **********),  # US number
        ("+************", 44, **********),  # UK number
    ],
)
def test_base_otp_handler_get_country_code_and_phone_number_valid(
    phone_number, expected_country_code, expected_phone_number
):
    handler = BaseOtpHandler()
    country_code, phone_number = handler.get_country_code_and_phone_number(
        phone_number
    )
    assert country_code == expected_country_code
    assert phone_number == expected_phone_number


@pytest.mark.unittest
@pytest.mark.parametrize("phone_number", ["invalid_number", "", None])
def test_base_otp_handler_get_country_code_and_phone_number_invalid_inputs(
    phone_number,
):
    handler = BaseOtpHandler()
    with pytest.raises(OTPException, match="Invalid phone number"):
        handler.get_country_code_and_phone_number(phone_number)


@pytest.mark.unittest
@patch("phonenumbers.parse")
def test_base_otp_handler_get_country_code_and_phone_number_parse_exception(
    mock_parse,
):
    mock_parse.side_effect = phonenumbers.NumberParseException(
        phonenumbers.NumberParseException.INVALID_COUNTRY_CODE,
        "Invalid country code",
    )
    handler = BaseOtpHandler()
    with pytest.raises(OTPException, match="Invalid phone number"):
        handler.get_country_code_and_phone_number("+invalid")


@pytest.mark.unittest
@pytest.mark.parametrize(
    "attempt_allowed,expected_result",
    [
        (True, False),  # Max attempts not reached
        (False, True),  # Max attempts reached
    ],
)
def test_base_otp_handler_is_send_max_attempts_reached(
    attempt_allowed, expected_result
):
    handler = BaseOtpHandler()
    mock_handler = MagicMock(spec=BaseOtpCacheHandler)
    mock_handler.attempt_allowed.return_value = attempt_allowed
    handler.otp_cache_handler = mock_handler

    result = handler.is_send_max_attempts_reached()
    assert result is expected_result
    mock_handler.attempt_allowed.assert_called_once()


@pytest.mark.unittest
def test_base_otp_handler_send_success():
    handler = BaseOtpHandler()
    mock_service = MagicMock(spec=BaseOtpApiService)
    handler.api_service = mock_service
    handler.send(country_code="91", phone_number="**********", otp="1234")
    mock_service.send_otp.assert_called_once_with(
        country_code="91", phone_number="**********", otp="1234"
    )


@pytest.mark.unittest
def test_base_otp_handler_send_api_exception():
    handler = BaseOtpHandler()
    mock_service = MagicMock(spec=BaseOtpApiService)
    mock_service.send_otp.side_effect = OTPApiException("API failed")
    handler.api_service = mock_service
    with pytest.raises(OTPException, match="Failed to send OTP"):
        handler.send(country_code="91", phone_number="**********", otp="1234")


@pytest.mark.unittest
def test_base_otp_handler_full_workflow_integration():
    handler = BaseOtpHandler()
    otp_handler = MagicMock(spec=BaseOtpCacheHandler)
    api_service = MagicMock(spec=BaseOtpApiService)
    otp_handler.attempt_allowed.return_value = True
    handler.otp_cache_handler = otp_handler
    handler.api_service = api_service
    assert not handler.is_send_max_attempts_reached()
    country_code, phone_number = handler.get_country_code_and_phone_number(
        "+************"
    )
    assert country_code == 91
    assert phone_number == **********
    handler.send(
        country_code=country_code, phone_number=phone_number, otp="1234"
    )
    api_service.send_otp.assert_called_once_with(
        country_code=91, phone_number=**********, otp="1234"
    )
