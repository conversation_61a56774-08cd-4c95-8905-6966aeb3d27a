from re import escape
from accounts.kyc.cache_handler import (
    GstOtpCacheHandler,
)
from accounts.kyc.exceptions import GstOTPMaxAttemptException

from accounts.utils.api_services.otp import OTPApiService
from accounts.utils.otp import BaseOtpHandler


class GstOtpHandler(BaseOtpHandler):
    def setup_send_otp(self, gst_number: str):

        self.otp_cache_handler: GstOtpCacheHandler = GstOtpCacheHandler(
            gst_number
        )
        self.api_service: OTPApiService = OTPApiService()

    def _send_otp(self, number: str, otp: str):
        country_code, phone_number = self.get_country_code_and_phone_number(
            number
        )
        self.send(country_code, phone_number, otp)

    def send_otp(self, number: str) -> int:
        if self.is_send_max_attempts_reached():
            raise GstOTPMaxAttemptException(
                f"Send OTP limit exceeded. Maximum allowed: {self.send_attempt_handler.MAX_ATTEMPTS}"
            )
        otp = self.generate_otp()
        self.otp_cache_handler.incr_attempt()
        self._send_otp(number, otp)
        self.otp_cache_handler.set_otp_data(number, otp)
        return otp
