from accounts.billing_accounts.utils.gst_parser import GSTDetail
from accounts.utils.cache_handler import BaseCacheHandler, BaseOtpCacheHandler


class KycOtpSendAttemptCacheHandler(BaseOtpCacheHandler):
    TTL = 60 * 60 * 24  # 24 hours
    MAX_ATTEMPTS = 5

    def __init__(self, gst_number: str):
        super().__init__(f"kyc:{gst_number}__otp_attempts")


class GstOtpCacheHandler(BaseOtpCacheHandler):
    MAX_ATTEMPTS = 5
    ATTEMPT_TTL = 60 * 60 * 24

    def __init__(self, gst_number: str):
        self.otp_key = f"kyc:{gst_number}__otp"
        self.attempt_key = f"kyc:{gst_number}__otp_attempts"

    def incr_attempt(self, ttl=ATTEMPT_TTL):
        self.incr(self.attempt_key, timeout=ttl)

    def attempt_allowed(self) -> bool:
        return super().attempt_allowed(self.MAX_ATTEMPTS)

    def set_otp_data(
        self, phone_number: str, otp: str, verification_attempts: int = 0
    ):
        data = {
            "phone_number": phone_number,
            "otp": otp,
            "verification_attempts": verification_attempts,
        }
        return super().save(self.otp_key, data, self.TTL)


class GstCacheHandler(BaseCacheHandler):
    GST_DATA_TTL = 60 * 60 * 24 * 10  # 10 days

    def set(self, gstDetail: GSTDetail, timeout=GST_DATA_TTL):
        self.save(gstDetail.data, timeout=timeout)
