import pytest
from unittest.mock import patch, MagicMock
from rest_framework.test import APIRequestFactory
from rest_framework import status
from rest_framework.exceptions import ValidationError

from accounts.billing_accounts.exceptions import InvalidGstNumberException
from accounts.exceptions import DependencyFailedException, ServerError
from accounts.kyc.exceptions import GstOTPMaxAttemptException
from accounts.billing_accounts.utils.gst_parser import GSTDetail
from accounts.kyc.views import GstSendOtpView


class TestKycSendOtpView:
    """Test KYC Send OTP View methods directly."""

    @pytest.fixture
    def view(self):
        return GstSendOtpView()

    @pytest.fixture
    def factory(self):
        return APIRequestFactory()

    @pytest.fixture
    def valid_gst_number(self):
        return "27AAPFU0939F1ZV"

    @pytest.fixture
    def invalid_gst_number(self):
        return "invalid_gst"

    @pytest.fixture
    def mock_gst_detail(self):
        mock_detail = MagicMock(spec=GSTDetail)
        mock_detail.mobile = "+************"
        mock_detail.gstin = "27AAPFU0939F1ZV"
        return mock_detail

    def test_validate_payload_valid_gst(self, view, factory, valid_gst_number):
        """Test validate_payload with valid GST number."""
        request = factory.post("/", {"gst_number": valid_gst_number})
        # Should not raise any exception
        view.validate_payload(request)

    def test_validate_payload_invalid_gst(
        self, view, factory, invalid_gst_number
    ):
        """Test validate_payload with invalid GST number."""
        request = factory.post("/", {"gst_number": invalid_gst_number})

        with pytest.raises(ValidationError) as exc_info:
            view.validate_payload(request)

        assert "Invalid GST Number" in str(exc_info.value)

    @patch("accounts.utils.api_services.surepass.SurepassApi.gstin_details")
    def test_get_gst_data_success(
        self, mock_gstin_details, view, valid_gst_number, mock_gst_detail
    ):
        """Test get_gst_data method with successful API call."""
        mock_gstin_details.return_value = mock_gst_detail

        result = view.get_gst_data(valid_gst_number)

        assert result == mock_gst_detail
        mock_gstin_details.assert_called_once_with(valid_gst_number)

    @patch("accounts.utils.api_services.surepass.SurepassApi.gstin_details")
    def test_get_gst_data_invalid_gst_exception(
        self, mock_gstin_details, view, valid_gst_number
    ):
        """Test get_gst_data method when API raises InvalidGstNumberException."""
        mock_gstin_details.side_effect = InvalidGstNumberException(
            "Invalid GSTIN"
        )

        with pytest.raises(DependencyFailedException) as exc_info:
            view.get_gst_data(valid_gst_number)

        assert "Invalid GSTIN" in str(exc_info.value)

    def test_send_otp_invalid_gst_number(self):
        """Test OTP sending with invalid GST number."""
        response = self.client.post(
            self.url, self.invalid_payload, format="json"
        )

        # Verify response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("Invalid GST Number", str(response.data))

    @patch("accounts.utils.api_services.surepass.SurepassApi.gstin_details")
    def test_send_otp_invalid_gst_number_from_api(self, mock_gstin_details):
        """Test OTP sending when API returns invalid GST number."""
        # Setup mock to raise InvalidGstNumberException
        mock_gstin_details.side_effect = InvalidGstNumberException(
            "Invalid GSTIN number"
        )

        # Make request
        response = self.client.post(self.url, self.valid_payload, format="json")

        # Verify response
        self.assertEqual(
            response.status_code, status.HTTP_424_FAILED_DEPENDENCY
        )
        self.assertIn("Invalid GSTIN number", str(response.data))

    @patch("accounts.kyc.views.GstOtpHandler")
    @patch("accounts.utils.api_services.surepass.SurepassApi.gstin_details")
    def test_send_otp_max_attempts_exceeded(
        self, mock_gstin_details, mock_otp_handler_class
    ):
        """Test OTP sending when max attempts exceeded."""
        # Setup mocks
        mock_gstin_details.return_value = self.mock_gst_detail
        mock_otp_handler = MagicMock()
        mock_otp_handler_class.return_value = mock_otp_handler
        mock_otp_handler.send_otp.side_effect = GstOTPMaxAttemptException(
            "Max attempts exceeded"
        )

        # Make request
        response = self.client.post(self.url, self.valid_payload, format="json")

        # Verify response
        self.assertEqual(
            response.status_code, status.HTTP_429_TOO_MANY_REQUESTS
        )
        self.assertIn("Max attempts exceeded", str(response.data))

    @patch("accounts.kyc.views.GstOtpHandler")
    @patch("accounts.utils.api_services.surepass.SurepassApi.gstin_details")
    def test_send_otp_value_error_raises_server_error(
        self, mock_gstin_details, mock_otp_handler_class
    ):
        """Test OTP sending when ValueError occurs."""
        # Setup mocks
        mock_gstin_details.return_value = self.mock_gst_detail
        mock_otp_handler = MagicMock()
        mock_otp_handler_class.return_value = mock_otp_handler
        mock_otp_handler.send_otp.side_effect = ValueError("Some value error")

        # Make request
        response = self.client.post(self.url, self.valid_payload, format="json")

        # Verify response
        self.assertEqual(
            response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR
        )

    def test_send_otp_missing_gst_number(self):
        """Test OTP sending with missing GST number."""
        response = self.client.post(self.url, {}, format="json")

        # Verify response
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    @patch("accounts.kyc.views.GstOtpHandler")
    @patch("accounts.utils.api_services.surepass.SurepassApi.gstin_details")
    def test_send_otp_dependency_failed_exception(
        self, mock_gstin_details, mock_otp_handler_class
    ):
        """Test OTP sending when dependency fails."""
        # Setup mocks
        mock_gstin_details.side_effect = InvalidGstNumberException(
            "Service unavailable"
        )

        # Make request
        response = self.client.post(self.url, self.valid_payload, format="json")

        # Verify response
        self.assertEqual(
            response.status_code, status.HTTP_424_FAILED_DEPENDENCY
        )
        self.assertIn("Service unavailable", str(response.data))

    @patch("accounts.kyc.views.logger")
    @patch("accounts.kyc.views.GstOtpHandler")
    @patch("accounts.utils.api_services.surepass.SurepassApi.gstin_details")
    def test_send_otp_logs_value_error(
        self, mock_gstin_details, mock_otp_handler_class, mock_logger
    ):
        """Test that ValueError is properly logged."""
        # Setup mocks
        mock_gstin_details.return_value = self.mock_gst_detail
        mock_otp_handler = MagicMock()
        mock_otp_handler_class.return_value = mock_otp_handler
        mock_otp_handler.send_otp.side_effect = ValueError("Test error")

        # Make request
        response = self.client.post(self.url, self.valid_payload, format="json")

        # Verify logging
        mock_logger.error.assert_called_once()
        self.assertIn("ValueError", mock_logger.error.call_args[0][0])

    @patch("accounts.kyc.views.GstOtpHandler")
    @patch("accounts.utils.api_services.surepass.SurepassApi.gstin_details")
    def test_send_otp_workflow_integration(
        self, mock_gstin_details, mock_otp_handler_class
    ):
        """Test complete workflow integration."""
        # Setup mocks
        mock_gstin_details.return_value = self.mock_gst_detail
        mock_otp_handler = MagicMock()
        mock_otp_handler_class.return_value = mock_otp_handler

        # Make request
        response = self.client.post(self.url, self.valid_payload, format="json")

        # Verify complete workflow
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify all steps were called in order
        mock_gstin_details.assert_called_once_with(self.valid_gst_number)
        mock_otp_handler_class.assert_called_once()
        mock_otp_handler.setup_send_otp.assert_called_once_with(
            self.valid_gst_number
        )
        mock_otp_handler.send_otp.assert_called_once_with(
            self.mock_gst_detail.mobile
        )


@pytest.mark.unittest
class TestKycSendOtpViewPytest:
    """Pytest-style tests following the existing pattern."""

    @pytest.fixture
    def api_client(self):
        from rest_framework.test import APIClient

        return APIClient()

    @pytest.fixture
    def valid_payload(self):
        return {"gst_number": "27AAPFU0939F1ZV"}

    @pytest.fixture
    def mock_gst_detail(self):
        mock_detail = MagicMock(spec=GSTDetail)
        mock_detail.mobile = "+************"
        mock_detail.gstin = "27AAPFU0939F1ZV"
        return mock_detail

    @patch("accounts.kyc.views.GstOtpHandler")
    @patch("accounts.utils.api_services.surepass.SurepassApi.gstin_details")
    def test_kyc_send_otp_success(
        self,
        mock_gstin_details,
        mock_otp_handler_class,
        api_client,
        valid_payload,
        mock_gst_detail,
    ):
        """Test successful KYC OTP sending using pytest style."""
        # Setup mocks
        mock_gstin_details.return_value = mock_gst_detail
        mock_otp_handler = MagicMock()
        mock_otp_handler_class.return_value = mock_otp_handler

        # Make request
        response = api_client.post(
            "/kyc/send-otp/", valid_payload, format="json"
        )

        # Verify response
        assert response.status_code == status.HTTP_200_OK
        assert response.data["message"] == "OTP sent successfully"

        # Verify method calls
        mock_gstin_details.assert_called_once_with("27AAPFU0939F1ZV")
        mock_otp_handler.setup_send_otp.assert_called_once_with(
            "27AAPFU0939F1ZV"
        )
        mock_otp_handler.send_otp.assert_called_once_with(
            mock_gst_detail.mobile
        )

    @patch("accounts.utils.api_services.surepass.SurepassApi.gstin_details")
    def test_kyc_send_otp_dependency_failed(
        self, mock_gstin_details, api_client, valid_payload
    ):
        """Test KYC OTP sending when dependency fails."""
        # Setup mock to raise InvalidGstNumberException
        mock_gstin_details.side_effect = InvalidGstNumberException(
            "API service down"
        )

        # Make request
        response = api_client.post(
            "/kyc/send-otp/", valid_payload, format="json"
        )

        # Verify response
        assert response.status_code == status.HTTP_424_FAILED_DEPENDENCY
        assert "API service down" in str(response.data)

    def test_kyc_send_otp_invalid_gst_validation(self, api_client):
        """Test KYC OTP sending with invalid GST number."""
        invalid_payload = {"gst_number": "invalid_gst"}

        response = api_client.post(
            "/kyc/send-otp/", invalid_payload, format="json"
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Invalid GST Number" in str(response.data)
