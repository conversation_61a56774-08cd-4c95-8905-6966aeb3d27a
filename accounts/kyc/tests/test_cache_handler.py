from unittest.mock import patch

from accounts.kyc.cache_handler import (
    GstOtpCache<PERSON>and<PERSON>,
    GstCacheHandler,
)
from accounts.billing_accounts.utils.gst_parser import GSTDetail


class TestGstOtpCacheHandler:
    """Test cases for GstOtpCacheHandler."""

    def test_init(self):
        """Test initialization of GstOtpCacheHandler."""
        gst_number = "27AAPFU0939F1ZV"
        handler = GstOtpCacheHandler(gst_number)

        assert handler.otp_key == f"kyc:{gst_number}__otp"
        assert handler.attempt_key == f"kyc:{gst_number}__otp_attempts"
        assert handler.MAX_ATTEMPTS == 5
        assert handler.ATTEMPT_TTL == 60 * 60 * 24  # 24 hours

    @patch("accounts.utils.cache_handler.cache")
    def test_incr_attempt(self, mock_cache):
        """Test incrementing attempt count."""
        mock_cache.incr.return_value = 2

        gst_number = "27AAPFU0939F1ZV"
        handler = GstOtpCacheHandler(gst_number)

        handler.incr_attempt()

        mock_cache.incr.assert_called_once_with(
            f"kyc:{gst_number}__otp_attempts"
        )

    @patch("accounts.utils.cache_handler.cache")
    def test_incr_attempt_with_custom_ttl(self, mock_cache):
        """Test incrementing attempt count with custom TTL."""
        mock_cache.incr.side_effect = ValueError("Key does not exist")
        mock_cache.set.return_value = True
        custom_ttl = 3600

        gst_number = "27AAPFU0939F1ZV"
        handler = GstOtpCacheHandler(gst_number)

        handler.incr_attempt(ttl=custom_ttl)

        mock_cache.incr.assert_called_once_with(
            f"kyc:{gst_number}__otp_attempts"
        )
        mock_cache.set.assert_called_once_with(
            f"kyc:{gst_number}__otp_attempts", 1, timeout=custom_ttl
        )

    @patch("accounts.utils.cache_handler.cache")
    def test_attempt_allowed_under_limit(self, mock_cache):
        """Test attempt_allowed returns True when under limit."""
        mock_cache.get.return_value = 3

        gst_number = "27AAPFU0939F1ZV"
        handler = GstOtpCacheHandler(gst_number)

        result = handler.attempt_allowed()

        assert result is True
        mock_cache.get.assert_called_once_with(
            f"kyc:{gst_number}__otp_attempts"
        )

    @patch("accounts.utils.cache_handler.cache")
    def test_attempt_allowed_at_limit(self, mock_cache):
        """Test attempt_allowed returns False when at limit."""
        mock_cache.get.return_value = 5

        gst_number = "27AAPFU0939F1ZV"
        handler = GstOtpCacheHandler(gst_number)

        result = handler.attempt_allowed()

        assert result is False
        mock_cache.get.assert_called_once_with(
            f"kyc:{gst_number}__otp_attempts"
        )

    @patch("accounts.utils.cache_handler.cache")
    def test_set_otp_data(self, mock_cache):
        """Test setting OTP data in cache."""
        mock_cache.set.return_value = True

        gst_number = "27AAPFU0939F1ZV"
        phone_number = "+91**********"
        otp = "1234"
        verification_attempts = 2

        handler = GstOtpCacheHandler(gst_number)
        result = handler.set_otp_data(phone_number, otp, verification_attempts)

        expected_data = {
            "phone_number": phone_number,
            "otp": otp,
            "verification_attempts": verification_attempts,
        }

        assert result is True
        mock_cache.set.assert_called_once_with(
            f"kyc:{gst_number}__otp", expected_data, handler.OTP_TTL
        )


class TestGstCacheHandler:
    """Test cases for GstCacheHandler."""

    def test_init(self):
        """Test initialization of GstCacheHandler."""
        key = "test_key"
        handler = GstCacheHandler(key)

        assert handler._key == key
        assert handler.GST_DATA_TTL == 60 * 60 * 24 * 10  # 10 days

    @patch("accounts.utils.cache_handler.cache")
    def test_set_gst_detail_default_timeout(self, mock_cache):
        """Test setting GST detail with default timeout."""
        mock_cache.set.return_value = True

        # Create mock GST detail
        gst_data = {
            "gstin": "27AAPFU0939F1ZV",
            "legal_name": "Test Company",
            "contact_details": {
                "principal": {
                    "mobile": "**********",
                    "address": "123 Test Street, 400001",
                }
            },
            "gstin_status": "Active",
        }

        gst_detail = GSTDetail(
            data=gst_data,
            gstin="27AAPFU0939F1ZV",
            legal_name="Test Company",
            pincode="400001",
            address="123 Test Street, 400001",
            status="Active",
        )

        handler = GstCacheHandler("test_key")
        handler.set(gst_detail)

        mock_cache.set.assert_called_once_with(
            "test_key", gst_data, 60 * 60 * 24 * 10
        )

    @patch("accounts.utils.cache_handler.cache")
    def test_set_gst_detail_custom_timeout(self, mock_cache):
        """Test setting GST detail with custom timeout."""
        mock_cache.set.return_value = True
        custom_timeout = 3600  # 1 hour

        # Create mock GST detail
        gst_data = {
            "gstin": "27AAPFU0939F1ZV",
            "legal_name": "Test Company",
            "contact_details": {
                "principal": {
                    "mobile": "**********",
                    "address": "123 Test Street, 400001",
                }
            },
            "gstin_status": "Active",
        }

        gst_detail = GSTDetail(
            data=gst_data,
            gstin="27AAPFU0939F1ZV",
            legal_name="Test Company",
            pincode="400001",
            address="123 Test Street, 400001",
            status="Active",
        )

        handler = GstCacheHandler("test_key")
        handler.set(gst_detail, timeout=custom_timeout)

        mock_cache.set.assert_called_once_with(
            "test_key", gst_data, custom_timeout
        )
