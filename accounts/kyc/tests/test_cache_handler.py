import pytest

from accounts.kyc.cache_handler import (
    Gst<PERSON>tp<PERSON><PERSON><PERSON><PERSON><PERSON>,
    GstCacheHandler,
)
from accounts.billing_accounts.utils.gst_parser import GSTDetail


@pytest.mark.unittest
def test_gst_otp_handler_incr_attempt():
    gst_number = "27AAPFU0939F1ZV"
    handler = GstOtpCacheHandler(gst_number)
    handler.incr_attempt()
    assert handler.get(handler.attempt_key) == 1


@pytest.mark.unittest
def test_gst_otp_handler_attempt_allowed():
    gst_number = "27AAPFU0939F1ZV"
    handler = GstOtpCacheHandler(gst_number)
    for _ in range(4):
        handler.incr_attempt()

    handler.attempt_allowed() is True
    handler.incr_attempt()
    handler.attempt_allowed() is False


@pytest.mark.unittest
def test_gst_otp_handler_set_otp_data():
    """Test setting OTP data in cache."""

    gst_number = "27AAPFU0939F1ZV"
    phone_number = "+************"
    otp = "1234"
    verification_attempts = 2

    handler = GstOtpCacheHandler(gst_number)
    handler.set_otp_data(phone_number, otp, verification_attempts)

    expected_data = {
        "phone_number": phone_number,
        "otp": otp,
        "verification_attempts": verification_attempts,
    }

    assert handler.get(handler.otp_key) == expected_data


@pytest.mark.unittest
def test_gst_cache_handler_set():
    """Test setting GST detail in cache."""

    gst_data = {
        "gstin": "27AAPFU0939F1ZV",
        "legal_name": "Test Company",
        "contact_details": {
            "principal": {
                "mobile": "9876543210",
                "address": "123 Test Street, 400001",
            }
        },
        "gstin_status": "Active",
    }

    gst_detail = GSTDetail(
        data=gst_data,
        gstin="27AAPFU0939F1ZV",
        legal_name="Test Company",
        pincode="400001",
        address="123 Test Street, 400001",
        status="Active",
    )

    handler = GstCacheHandler("test_key")
    handler.set(gst_detail)

    assert handler.get() == gst_data
