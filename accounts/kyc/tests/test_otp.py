import pytest
from unittest.mock import patch, MagicMock

from accounts.kyc.otp import GstOtpHandler
from accounts.kyc.exceptions import GstOTPMaxAttemptException
from accounts.exceptions import OTPException


class TestGstOtpHandler:
    """Test cases for GstOtpHandler."""

    def test_setup_send_otp(self):
        """Test setup_send_otp method initializes all required handlers."""
        gst_number = "27AAPFU0939F1ZV"
        handler = GstOtpHandler()

        handler.setup_send_otp(gst_number)

        # Check that all handlers are initialized
        assert handler.otp_cache_handler is not None
        assert handler.api_service is not None

        # Check handler types
        from accounts.kyc.cache_handler import GstOtpCacheHandler
        from accounts.utils.api_services.otp import OTPApiService

        assert isinstance(handler.otp_cache_handler, GstOtpCacheHandler)
        assert isinstance(handler.api_service, OTPApiService)

    @patch("accounts.kyc.otp.GstOtpHandler.get_country_code_and_phone_number")
    @patch("accounts.kyc.otp.GstOtpHandler.send")
    def test_send_otp_internal(self, mock_send, mock_get_country_code):
        """Test _send_otp method."""
        mock_get_country_code.return_value = ("91", "**********")

        gst_number = "27AAPFU0939F1ZV"
        handler = GstOtpHandler()
        handler.setup_send_otp(gst_number)

        number = "+************"
        otp = "1234"

        handler._send_otp(number, otp)

        mock_get_country_code.assert_called_once_with(number)
        mock_send.assert_called_once_with("91", "**********", otp)

    @patch("accounts.kyc.otp.GstOtpHandler.is_send_max_attempts_reached")
    def test_send_otp_max_attempts_exceeded(self, mock_max_attempts):
        """Test send_otp raises exception when max attempts exceeded."""
        mock_max_attempts.return_value = True

        gst_number = "27AAPFU0939F1ZV"
        handler = GstOtpHandler()
        handler.setup_send_otp(gst_number)

        number = "+************"

        with pytest.raises(GstOTPMaxAttemptException) as exc_info:
            handler.send_otp(number)

        expected_message = f"Send OTP limit exceeded. Maximum allowed: {handler.otp_cache_handler.MAX_ATTEMPTS}"
        assert str(exc_info.value) == expected_message

    @patch("accounts.kyc.otp.GstOtpHandler._send_otp")
    @patch("accounts.kyc.otp.GstOtpHandler.is_send_max_attempts_reached")
    @patch("accounts.kyc.otp.GstOtpHandler.generate_otp")
    def test_send_otp_success(
        self, mock_generate_otp, mock_max_attempts, mock_send_otp
    ):
        """Test successful OTP sending."""
        mock_max_attempts.return_value = False
        mock_generate_otp.return_value = 1234

        gst_number = "27AAPFU0939F1ZV"
        handler = GstOtpHandler()
        handler.setup_send_otp(gst_number)

        # Mock the cache handler methods
        with patch.object(
            handler.otp_cache_handler, "incr_attempt"
        ) as mock_incr, patch.object(
            handler.otp_cache_handler, "set_otp_data", return_value=True
        ) as mock_set:

            number = "+************"
            result = handler.send_otp(number)

            assert result == 1234
            mock_incr.assert_called_once()
            mock_set.assert_called_once_with(number, 1234)
            mock_send_otp.assert_called_once_with(number, 1234)

    @patch("accounts.kyc.otp.GstOtpHandler._send_otp")
    @patch("accounts.kyc.otp.GstOtpHandler.is_send_max_attempts_reached")
    @patch("accounts.kyc.otp.GstOtpHandler.generate_otp")
    def test_send_otp_flow_integration(
        self, mock_generate_otp, mock_max_attempts, mock_send_otp
    ):
        """Test complete send_otp flow integration."""
        mock_max_attempts.return_value = False
        mock_generate_otp.return_value = 5678

        gst_number = "27AAPFU0939F1ZV"
        handler = GstOtpHandler()
        handler.setup_send_otp(gst_number)

        # Mock the cache handler methods
        with patch.object(
            handler.otp_cache_handler, "incr_attempt"
        ) as mock_incr, patch.object(
            handler.otp_cache_handler, "set_otp_data", return_value=True
        ) as mock_set:

            number = "+************"
            result = handler.send_otp(number)

            # Verify all steps were called in correct order
            mock_max_attempts.assert_called_once()
            mock_generate_otp.assert_called_once()
            mock_incr.assert_called_once()
            mock_set.assert_called_once_with(number, 5678)
            mock_send_otp.assert_called_once_with(number, 5678)
            assert result == 5678

    def test_send_otp_without_setup_raises_error(self):
        """Test that send_otp raises error if setup_send_otp not called."""
        handler = GstOtpHandler()

        with pytest.raises(AttributeError):
            handler.send_otp("+************")

    @patch("accounts.kyc.otp.GstOtpHandler.get_country_code_and_phone_number")
    def test_send_otp_invalid_phone_number(self, mock_get_country_code):
        """Test _send_otp with invalid phone number."""
        mock_get_country_code.side_effect = OTPException("Invalid phone number")

        gst_number = "27AAPFU0939F1ZV"
        handler = GstOtpHandler()
        handler.setup_send_otp(gst_number)

        with pytest.raises(OTPException, match="Invalid phone number"):
            handler._send_otp("invalid_number", "1234")

    def test_inheritance(self):
        """Test that GstOtpHandler inherits from BaseOtpHandler."""
        from accounts.utils.otp import BaseOtpHandler

        handler = GstOtpHandler()
        assert isinstance(handler, BaseOtpHandler)

    def test_setup_send_otp_creates_correct_cache_keys(self):
        """Test that setup_send_otp creates cache handlers with correct keys."""
        gst_number = "27AAPFU0939F1ZV"
        handler = GstOtpHandler()

        handler.setup_send_otp(gst_number)

        assert handler.otp_cache_handler.otp_key == f"kyc:{gst_number}__otp"
        assert (
            handler.otp_cache_handler.attempt_key
            == f"kyc:{gst_number}__otp_attempts"
        )
