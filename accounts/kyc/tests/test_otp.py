import pytest

from accounts.kyc.otp import GstOtpHandler
from accounts.kyc.exceptions import GstOTPMaxAttemptException
from accounts.exceptions import OTPException
from accounts.kyc.cache_handler import GstOtpCacheHandler
from accounts.utils.api_services.otp import OTPApiService


@pytest.mark.unittest
def test_gst_otp_handler_setup_send_otp():
    gst_number = "27AAPFU0939F1ZV"
    handler = GstOtpHandler()
    handler.setup_send_otp(gst_number)
    assert handler.otp_cache_handler is not None
    assert handler.api_service is not None
    assert isinstance(handler.otp_cache_handler, GstOtpCacheHandler)
    assert isinstance(handler.api_service, OTPApiService)


@pytest.mark.unittest
def test_gst_otp_handler_send_otp_success(mock_smsg_api):
    res = mock_smsg_api()
    gst_number = "27AAPFU0939F1ZV"
    handler = GstOtpHandler()
    handler.setup_send_otp(gst_number)
    assert handler.send_otp("+************", "1234") is not None
    assert res.call_count == 1
    assert handler.otp_cache_handler.get(handler.otp_cache_handler.otp_key) == {
        "phone_number": "+************",
        "otp": "1234",
        "verification_attempts": 0,
    }
    assert (
        handler.otp_cache_handler.get(handler.otp_cache_handler.attempt_key)
        == 1
    )


@pytest.mark.unittest
def test_gst_otp_handler_send_otp_without_custom_otp(mock_smsg_api):
    """Test send_otp generates OTP when not provided."""
    res = mock_smsg_api()
    gst_number = "27AAPFU0939F1ZV"
    handler = GstOtpHandler()
    handler.setup_send_otp(gst_number)

    # Call without providing OTP
    generated_otp = handler.send_otp("+************")

    assert generated_otp is not None
    assert isinstance(generated_otp, int)
    assert 1000 <= generated_otp <= 9999
    assert res.call_count == 1

    # Verify the generated OTP was stored
    cached_data = handler.otp_cache_handler.get(
        handler.otp_cache_handler.otp_key
    )
    assert cached_data["otp"] == str(generated_otp)
    assert cached_data["phone_number"] == "+************"


@pytest.mark.unittest
def test_gst_otp_handler_send_otp_max_attempts_exceeded():
    """Test send_otp raises exception when max attempts exceeded."""

    gst_number = "27AAPFU0939F1ZV"
    handler = GstOtpHandler()
    handler.setup_send_otp(gst_number)
    for _ in range(10):
        handler.otp_cache_handler.incr_attempt()

    number = "+************"

    with pytest.raises(GstOTPMaxAttemptException) as exc_info:
        handler.send_otp(number)

    expected_message = f"Send OTP limit exceeded. Maximum allowed: {handler.otp_cache_handler.MAX_ATTEMPTS}"
    assert str(exc_info.value) == expected_message


@pytest.mark.unittest
def test_gst_otp_handler_send_otp_without_setup_raises_error():
    """Test that send_otp raises error if setup_send_otp not called."""
    handler = GstOtpHandler()

    with pytest.raises(
        ValueError, match="OTP cache handler is not initialized"
    ):
        handler.send_otp("+************")


@pytest.mark.unittest
def test_gst_otp_handler_send_otp_invalid_number():
    gst_number = "27AAPFU0939F1ZV"

    handler = GstOtpHandler()
    handler.setup_send_otp(gst_number)
    with pytest.raises(OTPException, match="Invalid phone number"):
        handler.send_otp("invalid_number")
