from math import log
from rest_framework.views import APIView
from rest_framework.response import Response

from accounts.billing_accounts.exceptions import InvalidGstNumberException
from accounts.billing_accounts.utils.billing_account import is_valid_gst_no
from rest_framework import status
from rest_framework.exceptions import ValidationError

from accounts.billing_accounts.utils.gst_parser import (
    GSTDetail,
    SurepassGSTParser,
)
from accounts.exceptions import (
    BaseException,
    ServerError,
)
from accounts.exceptions import OTPException
from accounts.kyc.otp import GstOtpHandler
from accounts.kyc.cache_handler import (
    GstCacheHandler,
)
from accounts.utils.api_services.surepass import SurepassApi
import logging

logger = logging.getLogger(__name__)


class KycSendOtpView(APIView):

    def validate_payload(self, request):
        if not is_valid_gst_no(request.data.get("gst_number")):
            raise ValidationError(
                detail="Invalid GST Number!!!",
                code=status.HTTP_400_BAD_REQUEST,
            )

    def get_gst_data(self, gst_number: str) -> GSTDetail:
        gst_cache_handler = GstCacheHandler(gst_number)
        cached_data = gst_cache_handler.get()
        if cached_data:
            logger.info("Using cached GST data")
            logger.debug(cached_data)
            try:
                return SurepassGSTParser(cached_data).parse()
            except Exception as e:
                logger.error(
                    f"Error parsing cached GST data: {e}", exc_info=True
                )

        try:
            logger.info("Fetching GST data from API")
            gst_data: GSTDetail = SurepassApi().gstin_details(gst_number)
            logger.debug(gst_data.data)
            gst_cache_handler.set(gst_data)
            return gst_data
        except InvalidGstNumberException as e:
            raise OTPException(str(e)) from e

    def post(self, request, *args, **kwargs):
        """
        Handle the sending of OTP for KYC verification.
        """
        self.validate_payload(request)

        gst_number = request.data["gst_number"]
        gst_data: GSTDetail = self.get_gst_data(gst_number)

        otp_handler = GstOtpHandler()
        otp_handler.setup_send_otp(gst_number)
        try:
            otp_handler.send_otp(gst_data.mobile)
        except ValueError as error:
            logger.error(f"ValueError: {error} ", exc_info=True)
            raise ServerError()  # raising server error!!
        except BaseException as error:
            return Response(
                {
                    "status": "error",
                    "code": error.get_error_code(),
                    "message": str(error),
                    "errors": error.get_errors(),
                },
                error.get_http_status_code(),
            )
        # Logic to send OTP goes here
        return Response(
            {"message": "OTP sent successfully"}, status=status.HTTP_200_OK
        )
