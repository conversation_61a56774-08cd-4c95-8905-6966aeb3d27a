from rest_framework.views import APIView
from rest_framework.response import Response

from accounts.billing_accounts.exceptions import InvalidGstNumberException
from accounts.billing_accounts.utils.billing_account import is_valid_gst_no
from rest_framework import status
from rest_framework.exceptions import ValidationError

from accounts.billing_accounts.utils.gst_parser import (
    GSTDetail,
)
from accounts.exceptions import (
    BaseException,
    DependencyFailedException,
    ServerError,
)
from accounts.kyc.otp import GstOtpHandler

from accounts.utils.api_services.surepass import SurepassApi
import logging

logger = logging.getLogger(__name__)


class KycSendOtpView(APIView):
    def validate_payload(self, request):
        if not is_valid_gst_no(request.data.get("gst_number")):
            raise ValidationError(
                detail="Invalid GST Number!!!",
                code=status.HTTP_400_BAD_REQUEST,
            )

    def get_gst_data(self, gst_number: str) -> GSTDetail:
        try:
            return SurepassApi.gstin_details(gst_number)
        except InvalidGstNumberException as e:
            raise DependencyFailedException(str(e)) from e

    def post(self, request, *args, **kwargs):
        """
        Handle the sending of OTP for KYC verification.
        """
        self.validate_payload(request)

        gst_number = request.data["gst_number"]
        gst_data: GSTDetail = self.get_gst_data(gst_number)

        otp_handler = GstOtpHandler()
        otp_handler.setup_send_otp(gst_number)
        try:
            otp_handler.send_otp(gst_data.mobile)
        except ValueError as error:
            logger.error(f"ValueError: {error} ", exc_info=True)
            raise ServerError()  # raising server error!!
        except BaseException as error:
            return Response(
                {
                    "status": "error",
                    "code": error.get_error_code(),
                    "message": str(error),
                    "errors": error.get_errors(),
                },
                error.get_http_status_code(),
            )
        return Response(
            {"message": "OTP sent successfully"}, status=status.HTTP_200_OK
        )
